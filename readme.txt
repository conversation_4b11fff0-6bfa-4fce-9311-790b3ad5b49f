=== affilinet Performance Ads ===
Contributors: affilinet, teraone
Tags: Affiliate, affilinet, advertising, banner, performance marketing
Requires at least: 3.0.1
Tested up to: 5.0
Stable tag: 1.9.5
License: GPLv2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html

Integrate our data driven and automated performance display plugin into your WordPress platform and serve your users targeted ads in real time.

== Description ==

Integrate affilinet's data driven advertising technology and automated ad display services seamlessly into your WordPress platform and serve the right message, at the right time, to the right person. Dont't worry about ad codes. This plugin will handle all technical details for you.

This plugin comes with a statistic page where you can see your daily earnings. You will just have to enter your affilinet Webservice Password.

The benefits at a glance:

* Easy integration of intelligent display ad units into your site
* Quick and simple sign up to the affilinet platform
* Targeted adverts mean more relevancy for your readers and ultimately more earnings from your website

We’ve made it really easy to get started. If you need integration support, please watch our tutorial on Youtube: https://www.youtube.com/watch?v=AtZHeD1CK-E

If you have any questions or need support please contact <NAME_EMAIL>

== Installation ==

You can download the Plugin from Wordpress Plugin repository or install manually

= Install from Wordpress Plugin repository =
1. Search for 'affilinet Performance Ads'
2. Click on 'Install' and activate the plugin
3. Don't forget to enter your Publisher ID


= Or Install manually =
1. Download the Plugin
2. Extract and upload the folder `affilinet-performance-module` to the `/wp-content/plugins/` directory
3. Activate the plugin through the 'Plugins' menu in WordPress



After installing the affilinet Performance Ads Plugin, a new "affilinet" sub-menu will appear in the main WordPress Admin menu.

= Required settings =
1. Click the Settings link and select the affilinet office you will be working with, click save
2. Signup to affilinet using the embedded form, available from the Signup menu link
3. After submitting the form and validating your email address you will be sent another email containing your affilinet publisher ID store this somewhere safe, you will need to use it to access the affilinet publisher platform
4. Login to the [affilinet publisher platform](https://publisher.affili.net/) using your publisher ID and the password entered into the sign-up form
5. Use the main navigation in the following steps: Solutions > Web Services > Access data to reach the page where you aquire your Publisher Web Service password

[Screenshot affilinet Publisher Portal](http://plugins.svn.wordpress.org/affilinet-performance-module/assets/affilinet_Publisher_Portal.png "Affilinet Publisher Portal")

Click the Acquire password button and wait for the token to be loaded as shown [here](http://plugins.svn.wordpress.org/affilinet-performance-module/assets/affilinet_Publisher_Portal_2.png "Affilinet Publisher Portal")

Now that you have both your publisher ID and your Publisher Web services password you can open up your WordPress admin again, and navigate to Settings page within the affilinet plugin.  Enter your publisher ID and Web Services password as shown [here](http://plugins.svn.wordpress.org/affilinet-performance-module/assets/WordPress_Plugin_Settings.png "Plugin Settings") and click Save Changes:

If you do not already have an affilinet publisher account you will need to signup using the embedded form.  After submitting the form and validating your email address you will have access to the affilinet publisher platform

It may take up to 90 minutes for ads to show, additionally all publisher accounts will be assessed by our network quality before they can receive ads from our entire network of advertising brands. This process can take up to 48 hours during busy periods.

= If you have an affilinet Publisher Account: =

1. Click the Settings link and select the affilinet office you will be working with, click save.
2. Signup to the affilinet Performance Ads programme using the embedded form, available from the Signup menu link, make sure you click the "Existing Publisher" tab before proceeding to login – you don't need to create a new account!
3. After submitting the form login to the [affilinet publisher platform](https://publisher.affili.net/) using your publisher ID and the password entered into the sign-up form.
4. Use the main navigation in the following steps: Solutions > Web Services > Access data to reach the page where you aquire your Publisher Web Service password

[Screenshot Affilinet Publisher Portal](http://plugins.svn.wordpress.org/affilinet-performance-module/assets/affilinet_Publisher_Portal.png "Affilinet Publisher Portal")

Click the Acquire password button and wait for the token to be loaded as shown [here](http://plugins.svn.wordpress.org/affilinet-performance-module/assets/affilinet_Publisher_Portal_2.png "Affilinet Publisher Portal").

Now that you have both your Publisher Web services password you can open up your WordPress admin again, and navigate to Settings page within the affilinet plugin. Enter your publisher ID and Web Services password as shown [here](http://plugins.svn.wordpress.org/affilinet-performance-module/assets/WordPress_Plugin_Settings.png "Plugin Settings") and click Save Changes:

It may take up to 90 minutes for ads to show, additionally all publisher accounts will be assessed by our network quality before they can receive ads from our entire network of advertising brands. This process can take up to 48 hours during busy periods.


== Changelog ==

= 1.9.5 =
* Release Date: May 11, 2018
* Fix: Nusoap as a fallback if Soap Extension is missing

= 1.9.4 =
* Release Date: March 9, 2018
* Fix: Suppress warnings on file write for ads.txt

= 1.9.3 =
* Release Date: March 6, 2018
* Fix: Update ads.txt on Plugin version change

= 1.9.2 =
* Release Date: March 6, 2018
* Fix: Update ads.txt on Plugin activation

= 1.9.1 =
* Release Date: March 6, 2018
* Updating ads.txt as needed for integration

= 1.9.0 =
* Release Date: March 3, 2018
* RTB Header bidding added => increased eCPM and better Publisher Monetization

= 1.8.7 =
* Release Date: July 5, 2017
* SubId can be modified with the hook 'affilinet_subid_array'

= 1.8.6 =
* Release Date: March 29, 2017
* Change translation Text Domain

= 1.8.5 =
* Release Date: November 25, 2016
* Minor enhancements

= 1.8.4 =
* Release Date: November 9, 2016
* PHP 5.3 compatibility

= 1.8.3 =
* Release Date: October 24, 2016
* Enhances Error messages on login page
* Mobile ad sizes (country platform dependent)

= 1.8.2 =
* Release Date: July 25, 2016
* Supporting pages under https
* Enhanced compatibility with other plugins

= 1.8.1 =
* Release Date: August 26, 2015
* enhanced installation docs

= 1.8 =
* Release Date: August 17, 2015
* Disable PerformanceAds Netherlands
* add subid parameter to adcodes
* enhanced installation docs

= 1.7 =
* Release Date: July 6, 2015
* Report data shown by date of registration

= 1.6 =
* Release Date: June 29, 2015
* Fixing TinyMCE Button Image

= 1.4 =
* Release Date: June 16, 2015
* Adding SubID Parameter

= 1.3 =
* Release Date: June 11, 2015
* Initial release
